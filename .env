# 测试环境
VITE_APP_BASE_API = '/stage-api'
VITE_APP_SSO_API_PREFIX = '/sso-redirect'

#系统编码
VITE_APP_SYSTEM_CODE = 'bdh-front-group'

VITE_APP_GATEWAYPATH_SSO = 'sso-api'
VITE_APP_GATEWAYPATH_FRONTGROUP = 'bdh-front-group-api'
VITE_APP_GATEWAYPATH_AGRICMATCHINE = 'bdh-agricultural-machinery-api'
VITE_APP_GATEWAYPATH_LANDCONTRACT = 'bdh-landcontract-api'
VITE_APP_GATEWAYPATH_SUBSIDY = 'bdh-subsidy-api'
VITE_APP_GATEWAYPATH_AGRICULTURALSITUATION = 'bdh-agricultural-situation-api'
VITE_APP_GATEWAYPATH_BPM = 'bdh-bpm-api'
VITE_APP_GATEWAYPATH_COSTANALYSIS = 'bdh-agric-cost-analysis-api'
VITE_APP_GATEWAYPATH_LANDRESOURCE = 'land-resource-api'
# 业务相关
# 权限接口(注意与VITE_APP_BASE_API保持一致)
VITE_APP_AUTH_URL='/client/getAmpMenu'

# sso退出（get）
VITE_APP_SSO_LOGOUT_URL='/bdh-front-group-api/client/logout'

# 导航菜单-系统编码
VITE_SYSTEM_CODE = 'sysanimalhusbandry,bdh-aviation-mission,bdh-agric-cost-analysis,bdh-samic-confirm,
bdh-question-feedback,bdh-info-publish,systemlandcontract,bdh-agric-invest,bdh-bpm'

VITE_APP_PUBLIC_PATH='/apps/front-group/'
