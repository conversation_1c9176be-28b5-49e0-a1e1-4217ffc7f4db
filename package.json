{"name": "tx", "version": "3.6.4", "description": "农业生产管理平台", "author": "TX", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "tenant": "vite --mode tenant", "build:prod": "node --max_old_space_size=10240 node_modules/vite/bin/vite.js build --mode production", "build:stage": "node --max_old_space_size=10240 node_modules/vite/bin/vite.js build --mode staging", "build:tenant": "node --max_old_space_size=10240 node_modules/vite/bin/vite.js build --mode tenant", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/TX-Cloud.git"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@bdh-gis/mapbox-gl": "3.1.3", "@bdh-gis/mapbox-gl-area-navigation": "1.7.0", "@bdh-gis/mapbox-gl-back": "1.7.0", "@bdh-gis/mapbox-gl-baselayer": "1.7.0", "@bdh-gis/mapbox-gl-compare": "1.7.1", "@bdh-gis/mapbox-gl-compass": "1.7.0", "@bdh-gis/mapbox-gl-curtain": "1.7.1", "@bdh-gis/mapbox-gl-draw": "1.7.0", "@bdh-gis/mapbox-gl-edit": "1.7.1", "@bdh-gis/mapbox-gl-extern": "1.7.0", "@bdh-gis/mapbox-gl-extern-panel": "1.7.0", "@bdh-gis/mapbox-gl-fullscreen": "1.7.0", "@bdh-gis/mapbox-gl-layer": "1.7.0", "@bdh-gis/mapbox-gl-legend": "1.7.0", "@bdh-gis/mapbox-gl-link": "1.7.0", "@bdh-gis/mapbox-gl-locate": "1.7.3", "@bdh-gis/mapbox-gl-location": "1.7.0", "@bdh-gis/mapbox-gl-measure": "1.7.1", "@bdh-gis/mapbox-gl-more": "1.7.0", "@bdh-gis/mapbox-gl-print": "1.7.0", "@bdh-gis/mapbox-gl-rastertilefilter": "1.7.0", "@bdh-gis/mapbox-gl-roaming": "1.7.0", "@bdh-gis/mapbox-gl-search": "1.7.2", "@bdh-gis/mapbox-gl-terrain": "1.7.0", "@bdh-gis/mapbox-gl-textlayer": "1.7.0", "@bdh-gis/mapbox-gl-trackplay": "1.7.0", "@bdh-gis/mapbox-gl-utils": "1.7.0", "@bdh-gis/mapbox-gl-view": "1.7.0", "@bdh-gis/mapbox-gl-zoom": "1.7.0", "@bytemd/plugin-breaks": "^1.21.0", "@bytemd/plugin-external-links": "^1.3.0", "@bytemd/plugin-footnotes": "^1.12.4", "@bytemd/plugin-frontmatter": "^1.21.0", "@bytemd/plugin-gemoji": "^1.21.0", "@bytemd/plugin-gfm": "^1.21.0", "@bytemd/plugin-highlight": "^1.21.0", "@bytemd/plugin-math": "^1.21.0", "@bytemd/plugin-medium-zoom": "^1.21.0", "@bytemd/plugin-mermaid": "^1.21.0", "@bytemd/vue-next": "^1.21.0", "@element-plus/icons-vue": "2.3.1", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.11", "@iconify/iconify": "^3.1.1", "@microsoft/fetch-event-source": "^2.0.1", "@purge-icons/generated": "^0.9.0", "@turf/bearing": "^6.5.0", "@turf/turf": "^6.5.0", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/compiler-ssr": "^3.5.13", "@vue/server-renderer": "^3.5.13", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "0.28.1", "bignumber.js": "^9.3.0", "bpmn-js": "^17.9.2", "bpmn-js-properties-panel": "5.23.0", "bpmn-js-token-simulation": "^0.36.0", "bytemd": "^1.21.0", "camunda-bpmn-moddle": "^7.0.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dplayer": "^1.27.1", "echarts": "5.5.1", "element-plus": "2.7.6", "file-saver": "2.0.5", "flv.js": "^1.6.2", "fuse.js": "6.6.2", "geobuf": "^3.0.2", "highlight.js": "^11.11.1", "jquery": "^3.7.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "mammoth": "^1.9.0", "mathjs": "^14.4.0", "md5": "^2.3.0", "mitt": "^3.0.1", "mqtt": "5.0.5", "nprogress": "0.2.0", "numeral": "^2.0.6", "ol": "6.15.1", "pinia": "2.1.7", "pinyin-pro": "^3.26.0", "qrcode.vue": "3.4.0", "qrcodejs": "^1.0.0", "qs": "^6.12.0", "sortablejs": "^1.15.3", "steady-xml": "^0.1.0", "terraformer-wkt-parser": "^1.2.1", "terser": "5.3.8", "vite-plugin-qiankun": "1.0.15", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-i18n": "9.10.2", "vue-lazyload": "^3.0.0", "vue-pdf": "^4.3.0", "vue-router": "4.4.0", "vue-types": "^5.1.1", "vue3-json-viewer": "^2.3.0", "vue3-print-nb": "^0.1.4", "vue3-signature": "^0.2.4", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xe-utils": "^3.7.4"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "sass": "1.77.5", "unocss": "^66.1.2", "unplugin-auto-import": "0.17.6", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.7.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}