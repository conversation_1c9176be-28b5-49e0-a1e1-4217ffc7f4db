import compression from 'vite-plugin-compression'

export default function createCompression(env) {
    const { VITE_BUILD_COMPRESS } = env
    const plugin = []
    console.log("启动文件压缩算法")

    if (VITE_BUILD_COMPRESS && typeof VITE_BUILD_COMPRESS === 'string') {
        const compressList = VITE_BUILD_COMPRESS.split(',')
        if (compressList.includes('gzip')) {
            plugin.push(
                compression({
                    verbose: true,
                    disable: false,
                    ext: '.gz',
                    threshold: 10240,
                    algorithm: 'gzip',
                    deleteOriginFile: true,//是否保留原文件
                    include: /\.(js|css|html|json|txt|ico|woff2?|ttf|eot)$/i,
                    exclude: /node_modules/
                })
            )
        }
        if (compressList.includes('brotli')) {
            plugin.push(
                compression({
                    verbose: true,
                    disable: false,
                    ext: '.br',
                    threshold: 10240,
                    algorithm: 'brotliCompress',
                    deleteOriginFile: true,//是否保留原文件
                    include: /\.(js|css|html|json|txt|ico|png|jpg|jpeg|woff2?|ttf|eot)$/i,
                    exclude: /node_modules/
                })
            )
        }
    }

    return plugin
}
