import vue from '@vitejs/plugin-vue'
import VueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'

import createAutoImport from './auto-import'
import createSvgIcon from './svg-icon'
import createCompression from './compression'
import createSetupExtend from './setup-extend'

import ElementPlus from 'unplugin-element-plus/vite'
// import AutoImport from 'unplugin-auto-import/vite'

import qiankun from "vite-plugin-qiankun"

export default function createVitePlugins(viteEnv, isBuild = false) {
    const vitePlugins = [
        vue(),
        VueJsx(),
        UnoCSS(),
        ElementPlus({importStyle: 'sass',
            useSource: true}),
        qiankun("bdh-front-group", { useDevMode: true }),
    ]
    vitePlugins.push(createAutoImport())
    vitePlugins.push(createSetupExtend())
    vitePlugins.push(createSvgIcon(isBuild))
    isBuild && vitePlugins.push(...createCompression(viteEnv))
    return vitePlugins
}
