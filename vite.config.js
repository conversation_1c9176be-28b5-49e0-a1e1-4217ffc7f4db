import { defineConfig, loadEnv } from 'vite'
import VueJsx from '@vitejs/plugin-vue-jsx'
import path from 'path'
import createVitePlugins from './vite/plugins'
import UnoCSS from 'unocss/vite'
// import { visualizer } from 'rollup-plugin-visualizer';

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.tx.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.tx.vip/admin/，则设置 baseUrl 为 /admin/。
    base: env.VITE_APP_PUBLIC_PATH,
    configureWebpack: {
      output: {
        library: `bdh-front-group`,
        libraryTarget: 'umd', // 把微应用打包成 umd 库格式
       // jsonpFunction: `webpackJsonp_${name}`, // webpack 5 需要把 jsonpFunction 替换成 chunkLoadingGlobal
      },
    },
    plugins:  [
      ...createVitePlugins(env, command === 'build'),
      UnoCSS(), // 确保已启用插件
      // visualizer({
      //   open: true,
      //   filename: 'dist/stats.html',
      //   gzipSize: true,
      //   brotliSize: true,
      // }),
    ],

    assetsInclude: ['**/*.glb' ,'**/*.gltf', '**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.pdf', '**/*.docx', '**/*.md'
    , '**/*.xlsx', '**/*.xls', '**/*.pptx', '**/*.ppt', '**/*.doc', '**/*.xps', '**/*.txt', '**/*.zip',
      '**/*.rar', '**/*.7z', '**/*.mp4', '**/*.mp3', '**/*.wav', '**/*.wmv', '**/*.mkv', '**/*.flv',
      '**/*.m3u8', '**/*.woff2', '**/*.eot', '**/*.ttf', '**/*.svg', '**/*.woff', '**/*.otf', '**/*.eot', '**/*.glb'],
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // 添加 optimizeDeps 配置
    optimizeDeps: {
      // include: [
      //   'vue',
      //   'vue-router',
      //   'axios',
      //   'lodash-es',
      //   'dayjs',
      //   'element-plus',
      //   'echarts',
      //   'xlsx',
      //   'pdfjs-dist',
      //   'mammoth',
      //   '@bdh-gis/mapbox-gl',
      //
      //   '@turf/turf',
      //   'ol', // OpenLayers
      //   '@bytemd/vue-next',
      //   '@form-create/element-ui',
      //   '@vueuse/core',
      // ]
    },
    // vite 相关配置
    server: {
      port: 8090,
      host: true,
      open: true,
      proxy: {
        [env.VITE_APP_SSO_API_PREFIX]: {
          target: env.VITE_APP_SSO_API_IP,
          changeOrigin: true,
          rewrite: (p) => p.replace(env.VITE_APP_SSO_API_PREFIX, '')
        },
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_APP_BASE_API_IP,
          changeOrigin: true,
          rewrite: (p) => p.replace(env.VITE_APP_BASE_API, '')
        }
        //其他代理请使用网关转发，需要加网关路由请联系孙继成或魏荣鑫

      },
      // proxy: {
      //   // https://cn.vitejs.dev/config/#server-proxy
      //   '/dev-api': {
      //     target: 'http://localhost:8080',
      //     // target: 'http://localhost:9090/one',
      //     changeOrigin: true,
      //     ws: true,
      //     rewrite: (p) => p.replace(/^\/dev-api/, '')
      //   }
      // }
    },
    //fix:error:stdin>:7356:1: warning: '@charset" must be the first rule in the file
    css: {
      // extract: false,
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    },
    build: {
      chunkSizeWarningLimit: 1000, // 默认为 500KB
      rollupOptions: {
        output: {
          manualChunks(id) {


            // 1. 第三方库单独拆包
            if (id.includes('node_modules')) {
              if (id.includes('@bdh-gis/mapbox-gl')) return 'chunk-mapbox';
              if (id.includes('@form-create')) return 'chunk-form-create';
              if (id.includes('@wangeditor')) return 'chunk-wangeditor';
              if (id.includes('cherry-markdown')) return 'chunk-cherry-markdown';
              if (id.includes('@bytemd')) return 'chunk-byte-md';
              if (id.includes('vue3-markdown-it')) return 'chunk-vue3-markdown-it';
              if (id.includes('echarts')) return 'chunk-echarts';
              if (id.includes('element-plus')) return 'chunk-element-plus';
              // if (id.includes('openlayers') || id.includes('ol')) return 'chunk-openlayers';
              if (id.includes('pdfjs-dist')) return 'chunk-pdfjs';
              if (id.includes('mammoth')) return 'chunk-mammoth';
              if (id.includes('bpmn')) return 'chunk-bpmn';
              // if (id.includes('ol')) return 'chunk-ol';
              // if (id.includes('mathjs')) return 'chunk-mathjs';
              if (id.includes('flv')) return 'chunk-flv';
              if (id.includes('pinyin-pro')) return 'chunk-pinyin-pro';
              if (id.includes('quill')) return 'chunk-vue-quill';
              if (id.includes('mqtt')) return 'chunk-mqtt';
              if (id.includes('dplayer')) return 'chunk-dplayer';
              // 剩余的小型依赖统一打包到 vendor
              return 'chunk-vendor';
            }


            // 2. 业务模块按前缀合并
            // if (id.includes('src/views/')) {
            //   const match = id.match(/src[\\/]views[\\/](bdh-[^\\/]+|sys[^\\/]+|error|redirect)/);
            //   if (match) {
            //     return `chunk-${match[1]}`;
            //   }
            // }
            //
            // // 3. 其他通用模块
            // if (id.includes('src/')) {
            //   if (id.includes('src/components') || id.includes('src/utils') || id.includes('src/directives')) {
            //     return 'chunk-common';
            //   }
            // }
          }
        }
      }
    }
  }
})
