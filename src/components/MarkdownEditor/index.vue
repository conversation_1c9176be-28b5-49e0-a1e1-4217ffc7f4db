<template>
  <div class="h-full w-full">
<!--    <Editor-->
<!--        id="d-Editor"-->
<!--        class="editor"-->
<!--        :locale="zhHans"-->
<!--        :plugins="plugins"-->
<!--        :uploadImages="uploadImages"-->
<!--        :value="markdownContent"-->
<!--        mode="split"-->
<!--        placeholder="请输入内容..."-->
<!--        @change="handleChange"-->
<!--    />-->
  </div>
</template>

<!--<script lang="ts" setup>-->
<!--// @ts-ignore , Viewer-->
<!--import { Editor, Viewer } from "@bytemd/vue-next"-->
<!--import gfm from "@bytemd/plugin-gfm"-->
<!--import zhHans from "bytemd/locales/zh_Hans.json"-->
<!--import footnotes from "@bytemd/plugin-footnotes"-->
<!--import frontmatter from "@bytemd/plugin-frontmatter"-->
<!--import highlight from "@bytemd/plugin-highlight"-->
<!--import mediumZoom from "@bytemd/plugin-medium-zoom"-->
<!--import gemoji from "@bytemd/plugin-gemoji"-->
<!--import math from "@bytemd/plugin-math"-->
<!--import breaks from "@bytemd/plugin-breaks"-->
<!--import mermaid from "@bytemd/plugin-mermaid"-->
<!--import externalLinks from "@bytemd/plugin-external-links"-->
<!--import 'juejin-markdown-themes/dist/github.css'-->


<!--import pluginMermaidZhHans from "@bytemd/plugin-mermaid/locales/zh_Hans.json"-->
<!--import pluginGfmZhHans from "@bytemd/plugin-gfm/locales/zh_Hans.json"-->
<!--import pluginMathZhHans from "@bytemd/plugin-math/locales/zh_Hans.json"-->


<!--const plugins = [-->
<!--  gfm({ locale: pluginGfmZhHans }),-->
<!--  highlight(),-->
<!--  mediumZoom(),-->
<!--  gemoji(),-->
<!--  math({ locale: pluginMathZhHans }),-->
<!--  mermaid({ locale: pluginMermaidZhHans }),-->
<!--  breaks(),-->
<!--  footnotes(),-->
<!--  frontmatter(),-->
<!--  externalLinks({ test: href => true })-->
<!--]-->

<!--import { ref, defineEmits,defineProps } from 'vue';-->
<!--//获取父组建传递值-->
<!--const props = defineProps(['valueMd']);-->
<!--// const value = props.valueMd-->

<!--const markdownContent = ref(props.valueMd || "")-->
<!--// markdownContent.value=props.valueMd-->
<!--//向父窗体传递值-->
<!--const emits = defineEmits(['returnValue']);-->
<!--//值改变触发展示-->
<!--const isInitializing = ref(true);-->
<!--function handleChange(val: string) {-->
<!--  markdownContent.value = val-->
<!--  markdownContent.value = val;-->
<!--  if (isInitializing.value && (markdownContent.value == null || markdownContent.value == '')) {-->
<!--    markdownContent.value = props.valueMd;-->
<!--  }-->
<!--  emits('returnValue', val);-->

<!--  if (isInitializing.value) {-->
<!--    isInitializing.value = false;-->
<!--  }-->
<!--}-->
<!--// handleChange('100万')-->

<!--function uploadImages() {-->

<!--}-->

<!--</script>-->
<style>

.bytemd-fullscreen.bytemd {
  z-index: 9999;
}

.editor, .ql-toolbar {
  white-space: normal !important;
}
</style>




