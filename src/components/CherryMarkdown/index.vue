<template>
  <div v-loading="loading" element-loading-text="文件上传中...">
<!--    <div @click.prevent.stop>-->
<!--      <div :id="mdId" :style="{ height: height + 'px' }"></div>-->
<!--    </div>-->
  </div>
</template>

<!--<script setup>-->

<!--import { ElMessage } from 'element-plus'-->
<!--import 'cherry-markdown/dist/cherry-markdown.min.css';-->
<!--import {ref} from 'vue';-->
<!--import Cherry from "cherry-markdown";-->
<!--import { getToken } from '@/utils/auth';-->

<!--const props = defineProps({-->
<!--  //各项目前缀-->
<!--  urlApi: {-->
<!--    type: String,-->
<!--    default: "",-->
<!--  },-->
<!--  height: {-->
<!--    type: Number,-->
<!--    default: 400,-->
<!--  },-->
<!--  modelValue: {-->
<!--    type: String,-->
<!--    default: "",-->
<!--  },-->
<!--  mdId: {-->
<!--    type: String,-->
<!--    default: "markdown-container",-->
<!--  }});-->
<!--const uploadFileUrl = ref(window.VITE_APP_BASE_API + `/${props.urlApi}/file/upload`);-->

<!--const emits = defineEmits(["update:modelValue", "returnValue"]);-->
<!--const cherrInstance = ref(null);-->
<!--const loading = ref(false);-->
<!--const { proxy } = getCurrentInstance();-->
<!--onMounted(() => {-->
<!--  //初始化markDown-->
<!--  initCherryMD();-->
<!--});-->

<!--const initCherryMD = (value, config) => {-->
<!--  cherrInstance.value = new Cherry({-->
<!--    id: props.mdId,-->
<!--    value: props.modelValue,-->
<!--    fileUpload: fileUpload,-->
<!--    emoji: {-->
<!--      useUnicode: true,-->
<!--    },-->
<!--    header: {-->
<!--      anchorStyle: "autonumber",-->
<!--    },-->
<!--    editor: {-->
<!--      defaultModel: "editOnly",-->
<!--    },-->
<!--    toolbars: {-->
<!--      theme: "light",-->
<!--      toolbar: [-->
<!--        "bold",-->
<!--        "italic",-->
<!--        "underline",-->
<!--        "strikethrough",-->
<!--        "|",-->
<!--        "color",-->
<!--        "header",-->
<!--        "|",-->
<!--        "list",-->
<!--        // "image",-->
<!--        {-->
<!--          insert: [-->
<!--            "audio",-->
<!--            // "video",-->
<!--            // "link",-->
<!--            "hr",-->
<!--            "br",-->
<!--            "code",-->
<!--            "formula",-->
<!--            "toc",-->
<!--            "table",-->
<!--            "line-table",-->
<!--            "bar-table",-->
<!--            // "pdf",-->
<!--            // "word",-->
<!--          ],-->
<!--        },-->
<!--        "graph",-->
<!--        "settings",-->
<!--        // "switchModel",-->
<!--        "togglePreview",-->
<!--      ],-->
<!--      bubble: [-->
<!--        "bold",-->
<!--        "italic",-->
<!--        "underline",-->
<!--        "strikethrough",-->
<!--        "sub",-->
<!--        "sup",-->
<!--        "|",-->
<!--        "size",-->
<!--        "color",-->
<!--      ],-->
<!--      float: [-->
<!--        "h1",-->
<!--        "h2",-->
<!--        "h3",-->
<!--        "|",-->
<!--        "checklist",-->
<!--        "quote",-->
<!--        "quickTable",-->
<!--        "code",-->
<!--      ],-->
<!--      customMenu: [],-->
<!--    },-->
<!--    callback: {-->
<!--      afterChange: afterChange,-->
<!--      beforeImageMounted: beforeImageMounted,-->
<!--    },-->
<!--  });-->
<!--};-->

<!--// 上传通用接口未实现audioVideo-->
<!--const fileUpload = (file, callback) => {-->
<!--  if (file.size / 1024 / 1024 > 200) {-->
<!--    return proxy.$modal.msgError("请上传200M以内的图片！");-->
<!--  }-->
<!--  if (!file.type.includes("image")) {-->
<!--    return proxy.$modal.msgError("仅支持上传图片！");-->
<!--  }-->
<!--  const formData = new FormData();-->
<!--  formData.append("file", file);-->
<!--  console.log(file, "file");-->
<!--  const request = new XMLHttpRequest();-->
<!--  // 图片上传路径修改为自己连接-->
<!--  request.open('POST', uploadFileUrl.value);-->
<!--  request.setRequestHeader('Authorization', "Bearer " + getToken());-->
<!--  request.onload = onloadCallback;-->
<!--  request.send(formData);-->
<!--};-->

<!--//上传加载回调-->
<!--function onloadCallback(oEvent) {-->
<!--  const currentTarget = oEvent.currentTarget;-->
<!--  if (currentTarget.status!== 200) {-->
<!--    return ElMessage({-->
<!--      type: 'error',-->
<!--      message: currentTarget.status + ' ' + currentTarget.statusText-->
<!--    });-->
<!--  }-->
<!--  const resp = JSON.parse(currentTarget.response);-->
<!--  let imgMdStr = '';-->
<!--  if (resp.code!== 200) {-->
<!--    return ElMessage({-->
<!--      type: 'error',-->
<!--      message: resp.msg-->
<!--    });-->
<!--  }-->
<!--    console.error(resp)-->
<!--  if (resp.code === 200) {-->
<!--    const fileName = resp.data.name.split('.');-->
<!--    const fileExt = fileName[fileName.length - 1];-->
<!--    if (/mp4|avi|rmvb/i.test(fileExt)) {-->
<!--      imgMdStr = `!video[${resp.data.name}](${resp.data.url})`;-->
<!--    } else if (/mp3/i.test(fileExt)) {-->
<!--      imgMdStr = `!audio[${resp.data.name}](${resp.data.url})`;-->
<!--    } else if (/bmp|gif|jpg|jpeg|png/i.test(fileExt)) {-->
<!--      imgMdStr = `![${resp.data.name}](${resp.data.url})`;-->
<!--    } else {-->
<!--      imgMdStr = `[${resp.data.name}](${resp.data.url})`;-->
<!--    }-->
<!--  }-->
<!--  cherrInstance.value.insert(imgMdStr);-->
<!--}-->


<!--// 变更事件回调-->
<!--const afterChange = (e) => {-->
<!--  emits("returnValue", getCherryContent(), getCherryHtml());-->
<!--};-->

<!--// 获取渲染后html内容-->
<!--const getCherryHtml = () => {-->
<!--  const result = cherrInstance.value.getHtml();-->
<!--  // console.log(result, "get");-->
<!--  return result;-->
<!--};-->

<!--// 获取markdown内容-->
<!--const getCherryContent = () => {-->
<!--  const result = cherrInstance.value.getMarkdown();-->
<!--  return result;-->
<!--};-->

<!--// 设置markdown内容-->
<!--const setCherryContent = (val) => {-->
<!--  cherrInstance.value.setMarkdown(val, 1);-->
<!--};-->

<!--// 图片加载回调-->
<!--const beforeImageMounted = (e, src) => {-->
<!--  return { [e]: src };-->
<!--};-->
<!--defineExpose({-->
<!--  getCherryHtml,-->
<!--  setCherryContent,-->
<!--});-->
<!--</script>-->

<!--<style scoped>-->
<!--/* 可以在这里添加样式来调整Cherry Markdown的显示外观 */-->
<!--</style>-->
