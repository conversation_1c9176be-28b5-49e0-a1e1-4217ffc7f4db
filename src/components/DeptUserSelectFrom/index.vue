<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧部分 -->
      <el-col :span="10" :xs="24" class="with-border">
        <!-- 组织机构 -->
        <div class="head-container">
          <el-input
              v-model="deptName"
              placeholder="请输入部门名称"
              clearable
              prefix-icon="Search" style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container tree-container" style="height: 200px">
          <el-tree
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="deptTreeRef"
              node-key="id"
              highlight-current
              default-expand-all
              @node-click="handleNodeClick"
          />
        </div>
        <!-- 机构下人员列表 -->
        <div class="head-container" style="height: 300px">
          <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" />
            <el-table-column label="用户昵称" align="center" key="nickName" prop="nickName" v-if="columns[2].visible" :show-overflow-tooltip="true" />
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button type="primary" size="small" @click="addSelectedUser(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <!-- 右侧部分 -->
      <el-col :span="14" :xs="24" class="with-border">
        <div class="head-container">
          <h3>已选择人员 ({{ selectedUsers.length }})</h3>
        </div>
        <div class="head-container">
          <el-table :data="selectedUsers">
            <el-table-column label="用户编号" align="center" key="userId" prop="userId" />
            <el-table-column label="用户昵称" align="center" key="nickName" prop="nickName" :show-overflow-tooltip="true" />
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button type="danger" size="small" @click="removeSelectedUser(scope.row)">取消选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <!-- 底部按钮 -->
    <div class="footer-container">
      <el-button type="primary" @click="confirmSelection">确认</el-button>
      <el-button @click="cancelSelection">取消</el-button>
    </div>
  </div>
</template>


<script setup name="User">
import { getToken } from "@/utils/auth";
import { listUser, deptTreeSelect } from "@/api/system/user";
import { defineEmits } from 'vue';
const emit = defineEmits(['confirm', 'cancel']);
const { proxy } = getCurrentInstance();
const { sys_normal_disable, sys_user_sex } = proxy.useDict("sys_normal_disable", "sys_user_sex");

const props = defineProps({
  selectedUsers: {
    type: Array,
    default: () => []
  }
});

const userList = ref([]);
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const deptName = ref("");
const deptOptions = ref(undefined);
const selectedUsers = ref([]);


onMounted(() => {
  selectedUsers.value = [...props.selectedUsers];
});

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: window.VITE_APP_BASE_API + "/system/user/importData"
});
// 列显隐信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `用户名称`, visible: true },
  { key: 2, label: `用户昵称`, visible: true },
  { key: 3, label: `部门`, visible: true },
  { key: 4, label: `手机号码`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `创建时间`, visible: true }
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined,
    deptId: undefined
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watch(deptName, val => {
  proxy.$refs["deptTreeRef"].filter(val);
});

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data;
  });
};

/** 查询用户列表 */
function getList() {
  loading.value = true;
  listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then(res => {
    loading.value = false;
    userList.value = res.rows;
    total.value = res.total;
  });
};

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
};

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.deptId = undefined;
  proxy.$refs.deptTreeRef.setCurrentKey(null);
  handleQuery();
};

/** 选择条数  */
function handleSelectionChange(selection) {
  selectedUsers.value = selection;
  ids.value = selection.map(item => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

// 添加用户到已选择列表
function addSelectedUser(user) {
  const index = selectedUsers.value.findIndex(item => item.userId === user.userId);
  if (index === -1) {
    selectedUsers.value.push(user);
  }
}

// 移除已选择的用户
function removeSelectedUser(user) {
  const index = selectedUsers.value.findIndex(item => item.userId === user.userId);
  if (index !== -1) {
    selectedUsers.value.splice(index, 1);
  }
}

// 确认选择
function confirmSelection() {
  if (selectedUsers.value.length === 0) {
    proxy.$modal.msgWarning("未选择人员");
    return;
  }
  emit('confirm', selectedUsers.value);
}

// 取消选择
function cancelSelection() {
  emit('cancel');
}

getDeptTree();
getList();
</script>


<style scoped>
.tree-container {
  overflow-y: auto;
}
.with-border {
  border: 1px solid #dcdcdc;
  padding: 10px;
  margin-bottom: 10px;
}
.footer-container {
  display: flex;
  justify-content: right;
  margin-top: 20px;
}
</style>
