
<template>
  <div class="icon-body">
    <el-input
        v-model="iconName"
        class="icon-search"
        clearable
        placeholder="请输入图标名称"
        @clear="filterIcons"
        @input="filterIcons"
    >
      <template #suffix><i class="el-icon-search el-input__icon" /></template>
    </el-input>
    <div class="icon-list">
      <div class="list-container">
        <div v-for="(item, index) in iconList" class="icon-item-wrapper" :key="index" @click="selectedIcon(item)">
          <div :class="['icon-item', { active: activeIcon === item }]">
            <img :src="`${iconPath}/${item}`" class="icon" style="height: 40px; width: 40px;" />
            <span>{{ item }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import navigationIcon from './requireIconsNav'
import {ref, defineProps, defineEmits, defineExpose} from 'vue';

const props = defineProps({
  activeIcon: {
    type: String,
    default: ''
  },
  iconPath: {
    type: String,
    default: '/src/assets/navigation_icons'
  }
});

const iconName = ref('');
const iconList = ref(getIconFiles(props.iconPath));
const emit = defineEmits(['selected']);

function filterIcons() {
  iconList.value = getIconFiles(props.iconPath);
  if (iconName.value) {
    iconList.value = iconList.value.filter(item => item.toLowerCase().indexOf(iconName.value.toLowerCase()) !== -1);
  }
}

function selectedIcon(name) {
  emit('selected', name);
  document.body.click();
}

function reset() {
  iconName.value = '';
  iconList.value = getIconFiles(props.iconPath);
}

function getIconFiles(path) {
  console.log(navigationIcon)
    return navigationIcon.filter(item => item.indexOf(iconName.value) !== -1)
}


defineExpose({
  reset
});
</script>

<style lang="scss" scoped>.icon-body {
  width: 100%;
  padding: 10px;

  .icon-search {
    position: relative;
    margin-bottom: 5px;
  }

  .icon-list {
    height: 200px;
    overflow: auto;

    .list-container {
      display: flex;
      flex-wrap: wrap;

      .icon-item-wrapper {
        width: calc(100% / 3);
        height: 45px;
        line-height: 25px;
        cursor: pointer;
        display: flex;

        .icon-item {
          display: flex;
          max-width: 100%;
          height: 100%;
          padding: 0 5px;

          &:hover {
            background: #ececec;
            border-radius: 5px;
          }

          .icon {
            flex-shrink: 0;
          }

          span {
            display: inline-block;
            vertical-align: -0.15em;
            fill: currentColor;
            padding-left: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .icon-item.active {
          background: #ececec;
          border-radius: 5px;
        }
      }
    }
  }
}
</style>
