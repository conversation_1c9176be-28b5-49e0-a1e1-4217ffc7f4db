<template>
  <div style="justify-content: center;align-items: center;display: flex;">
    <el-badge :value="16" class="item help-el-badge">
      <svg-icon class-name="customer-service-svg" icon-class="客服" @click="goto" />
    </el-badge>
    <span class="svg-text-span">客服工单</span>
  </div>
</template>

<script setup>
const url = ref('https://yiyan.baidu.com/');

function goto() {
  window.open(url.value)
}
</script>
<style lang='scss' scoped>
.customer-service-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;
  font-size: 18px;
  margin-right: 3px;
}
.svg-text-span{
  margin-left: 3px;
  font-size: 14px;
}
.help-el-badge {
  --el-badge-radius: 10px;
  --el-badge-font-size: 10px;
  --el-badge-padding: 3px;
  --el-badge-size: 16px;
  line-height: 20px;
}
</style>