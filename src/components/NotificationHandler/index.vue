<template>
  <div style="width: 0; height: 0; overflow: hidden;">
    消息发送与接收控件
  </div>
</template>

<script setup>

import { getCurrentInstance } from 'vue'
import useNotificationStore from "@/store/modules/notificationWS.js";
const { proxy } = getCurrentInstance();
const notificationStore = useNotificationStore();

defineExpose({
  sendMessage,
  disconnect
})

const props = defineProps({
  uniqueId: {
    type: String,
    required: true
  },
  notificationTypes: {
    type: Array,
    required: true
  }
})

const notificationOptions = {
  uniqueId: props.uniqueId,
  type: props.notificationTypes,
  onMessage: onMessage,
};

// 在组件挂载时调用 getSysNoticeStaffByUser 方法
onMounted(async () => {
  console.debug('初始化', notificationOptions)
  await notificationStore.connection(notificationOptions);

});

onUnmounted( () => {
  console.debug('离开Notification', props.notificationTypes)
  notificationStore.disconnect(notificationOptions);
});

function onMessage(data) {
  console.debug("WebSocket 收到消息", data);
  proxy.$emit("onNotification",data);

}

function sendMessage(type, data) {
  notificationStore.sendMessage(type, data)
}

function disconnect() {
  notificationStore.disconnect(notificationOptions);
}


</script>

