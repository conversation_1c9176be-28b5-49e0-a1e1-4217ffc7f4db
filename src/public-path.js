import { qiankunWindow } from "vite-plugin-qiankun/dist/helper";

window.VITE_APP_BASE_API = import.meta.env.VITE_APP_BASE_API;

if (qiankunWindow.__POWERED_BY_QIANKUN__) {
  window.__POWERED_BY_QIANKUN__ = qiankunWindow.__POWERED_BY_QIANKUN__;
  // eslint-disable-next-line no-undef
  // __webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;

  // 作为微前端子应用，统一使用如下 api 前缀
  window.VITE_APP_BASE_API = '/bdh-portal/api'; // 这里要改成子应用的
}
