server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    gzip on;
    gzip_static on;
    gzip_min_length 2k;
    gzip_buffers 4 16k;
    gzip_http_version 1.0;
    gzip_comp_level 5;
    gzip_types text/plain text/css application/javascript application/x-javascript application/json image/jpeg image/gif image/png;
    gzip_vary on;
    client_max_body_size 0;
    location /wechat.html {
        root /usr/share/nginx/html;
    }
    location ~ /scanCode {
       rewrite ^/(.*) https://trace.bdhic.com/wechat.html permanent;
    }
    location / {
       root /usr/share/nginx/html;
       index index.html;
       try_files $uri $uri/  @router;
       proxy_set_header X-Real-IP    $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-NginX-Proxy true;
       add_header 'Access-Control-Allow-Origin' *;
       #允许请求的header
       add_header 'Access-Control-Allow-Headers' *;
       #允许带上cookie请求
       add_header 'Access-Control-Allow-Credentials' 'true';
       #允许请求的方法，比如 GET,POST,PUT,DELETE
       add_header 'Access-Control-Allow-Methods' *;
    }


    location @router {
      rewrite ^.*$ /index.html last;
    }

    location /${PROJECT_PATH}/ {
       gunzip on;
       proxy_set_header X-Real-IP    $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-NginX-Proxy true;
       add_header 'Access-Control-Allow-Origin' *;
       #允许请求的header
       add_header 'Access-Control-Allow-Headers' *;
       #允许带上cookie请求
       add_header 'Access-Control-Allow-Credentials' 'true';
       #允许请求的方法，比如 GET,POST,PUT,DELETE
       add_header 'Access-Control-Allow-Methods' *;

      client_body_buffer_size 256k;
      proxy_connect_timeout 1800;
      proxy_send_timeout 1800;
      proxy_read_timeout 1800;
      proxy_buffering on;
      proxy_buffer_size 256k;
      proxy_buffers 4 256k;
      proxy_busy_buffers_size 256k;
      proxy_temp_file_write_size 256k;
      proxy_max_temp_file_size 2048m;
      client_max_body_size 2048m;
      send_timeout 1800;

       proxy_pass  ${PROJECT_API};
    }

    location /webroot {
        proxy_pass  ${FANRUAN_URL};
        proxy_buffer_size  128k;
        proxy_buffers   32 32k;
        proxy_busy_buffers_size 128k;
        proxy_set_header authAccessSystem ${FANRUAN_ACCESS_CODE};
    }

   # 兼容微前端
    location ^~ /apps/front-group/ {
      rewrite ^/apps/front-group/(.*) /$1 break;
    }

    location /sso-redirect {
       rewrite ^/sso-redirect/(.*)$ ${SSO_API}$1 permanent;
    }

    location /geoservice-api/ {
      proxy_pass  ${GEOSERVICE_API};
    }
    location /geoservice-rvt-api/ {
      proxy_pass ${GEOSERVICE_RVT_API};
    }

    location /oss-static-api/ {
      proxy_pass ${OSS_STATIC_API}app/migrated-from-minio/;
    }
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}

